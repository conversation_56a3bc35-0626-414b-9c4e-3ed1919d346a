<template>
  <div>
    <div class="block">
      <h3>动态加载</h3>
      <!-- 动态级联：v-model绑定选中值，props需要提供{lazy, lazyLoad}, lazy为开启懒加载 -->
      <el-cascader
        ref="cascader"
        v-model="selectedOptions"
        :props="props"
        @change="handleChange"
        @expand-change="handleExpandChange"
      ></el-cascader>
    </div>
  </div>
</template>
<script>
import axios from 'axios';
export default {
  data() {
    return {
      props: {
        lazy: true,
        lazyLoad: this.lazyLoad,
      },
      selectedOptions: []
    }
  },
  methods: {
    handleChange(value) {
      console.log(value)
    },
    // 默认就会执行？先拿到第一级数据
    async lazyLoad (node, resolve) {
      const parentId = node.level === 0 ? null : node.value;
      console.log('前端请求 parentId: ', parentId, 'level:', node.level);

      try {
        const res = await this.fetchChildNodes(parentId);
        console.log('res: ', res);

        // 如果没有返回数据或数据为空，说明当前节点是叶子节点
        if (!res.data || res.data.length === 0) {
          resolve([]);
          return;
        }

        const nodes = res.data.map(item => ({
          value: item.value,
          label: item.label,
          // 方案选择：
          // 1. 如果后端返回了 hasChildren 或 isLeaf 字段，直接使用
          leaf: item.isLeaf || item.hasChildren === false,

          // 2. 或者根据层级限制（比如最多3级：省-市-区）
          // leaf: node.level >= 2,

          // 3. 或者根据数据的特定规则（比如某些特定的value表示叶子节点）
          // leaf: this.isLeafByRule(item)
        }));

        resolve(nodes);
      } catch (error) {
        console.error('加载数据失败:', error);
        resolve([]);
      }
    },
    handleExpandChange(nodes) {
      console.log('展开变化:', nodes);
      // 这个方法主要用于监听展开状态变化
      // 实际的选择逻辑应该通过 change 事件处理
    },
    async fetchChildNodes(parentId) {
      return axios.get('http://localhost:3000/api/regions', {
        params: { parentId },
      });
    },

    // 根据业务规则判断是否为叶子节点的辅助方法
    isLeafByRule(item) {
      // 示例：根据value的长度判断层级
      // 假设省份代码2位，市代码4位，区代码6位
      if (typeof item.value === 'string') {
        return item.value.length >= 6; // 6位及以上认为是最后一级
      }
      return false;
    },
  }
}
</script>

<style lang="scss" scoped>
.block {
  border: 1px solid #ddd;
}
</style>