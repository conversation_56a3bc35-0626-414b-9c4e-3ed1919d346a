<template>
  <div>
    <div class="block">
      <h3>动态加载</h3>
      <!-- 动态级联：v-model绑定选中值，props需要提供{lazy, lazyLoad}, lazy为开启懒加载 -->
      <el-cascader
        ref="cascader"
        v-model="selectedOptions"
        :props="props"
        @change="handleChange"
        @expand-change="handleExpandChange"
      ></el-cascader>
    </div>
  </div>
</template>
<script>
import axios from 'axios';
export default {
  data() {
    return {
      props: {
        lazy: true,
        lazyLoad: this.lazyLoad,
      },
      selectedOptions: []
    }
  },
  methods: {
    handleChange(value) {
      console.log(value)
    },
    // 默认就会执行？先拿到第一级数据
    async lazyLoad (node, resolve) {
      const parentId = node.level === 0 ? null : node.value;
      console.log('前端请求 parentId: ', parentId);
      const res = await this.fetchChildNodes(parentId);
      console.log('res: ', res);
      const nodes = res.data.map(item => ({
        value: item.value,
        label: item.label,
        leaf: false // 默认设为非叶子节点，点击时再判断
      }));
      resolve(nodes);
    },
    handleExpandChange(nodes) {
      const lastNode = nodes[nodes.length - 1];
      if (lastNode && lastNode.leaf) {
        // 如果是叶子节点，直接选中并关闭下拉
        this.selectedOptions = nodes.map(n => n.value);
        this.$refs.cascader.dropDownVisible = false;
      }
    },
    async fetchChildNodes(parentId) {
      return axios.get('http://localhost:3000/api/regions', {
        params: { parentId },
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.block {
  border: 1px solid #ddd;
}
</style>