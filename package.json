{"name": "demo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"axios": "^1.10.0", "vite": "^5.4.19"}, "dependencies": {"@vitejs/plugin-vue2": "^2.3.3", "element-ui": "^2.15.14", "sass-embedded": "^1.89.2", "vue": "^2.7.14", "vue-router": "^3.6.5", "vue-template-compiler": "^2.7.16", "vuex": "^3.6.2"}}